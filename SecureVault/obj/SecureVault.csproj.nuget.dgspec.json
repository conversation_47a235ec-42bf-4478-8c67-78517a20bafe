{"format": 1, "restore": {"/Users/<USER>/work space /secure-vault-cli/SecureVault/SecureVault.csproj": {}}, "projects": {"/Users/<USER>/work space /secure-vault-cli/SecureVault/SecureVault.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/work space /secure-vault-cli/SecureVault/SecureVault.csproj", "projectName": "SecureVault", "projectPath": "/Users/<USER>/work space /secure-vault-cli/SecureVault/SecureVault.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/work space /secure-vault-cli/SecureVault/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"VaultSharp": {"target": "Package", "version": "[1.17.5.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.406/PortableRuntimeIdentifierGraph.json"}}}}}