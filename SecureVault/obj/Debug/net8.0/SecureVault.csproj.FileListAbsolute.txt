/Users/<USER>/work space /secure-vault-cli/SecureVault/bin/Debug/net8.0/SecureVault
/Users/<USER>/work space /secure-vault-cli/SecureVault/bin/Debug/net8.0/SecureVault.deps.json
/Users/<USER>/work space /secure-vault-cli/SecureVault/bin/Debug/net8.0/SecureVault.runtimeconfig.json
/Users/<USER>/work space /secure-vault-cli/SecureVault/bin/Debug/net8.0/SecureVault.dll
/Users/<USER>/work space /secure-vault-cli/SecureVault/bin/Debug/net8.0/SecureVault.pdb
/Users/<USER>/work space /secure-vault-cli/SecureVault/bin/Debug/net8.0/VaultSharp.dll
/Users/<USER>/work space /secure-vault-cli/SecureVault/obj/Debug/net8.0/SecureVault.csproj.AssemblyReference.cache
/Users/<USER>/work space /secure-vault-cli/SecureVault/obj/Debug/net8.0/SecureVault.GeneratedMSBuildEditorConfig.editorconfig
/Users/<USER>/work space /secure-vault-cli/SecureVault/obj/Debug/net8.0/SecureVault.AssemblyInfoInputs.cache
/Users/<USER>/work space /secure-vault-cli/SecureVault/obj/Debug/net8.0/SecureVault.AssemblyInfo.cs
/Users/<USER>/work space /secure-vault-cli/SecureVault/obj/Debug/net8.0/SecureVault.csproj.CoreCompileInputs.cache
/Users/<USER>/work space /secure-vault-cli/SecureVault/obj/Debug/net8.0/SecureVa.0D5DC99B.Up2Date
/Users/<USER>/work space /secure-vault-cli/SecureVault/obj/Debug/net8.0/SecureVault.dll
/Users/<USER>/work space /secure-vault-cli/SecureVault/obj/Debug/net8.0/refint/SecureVault.dll
/Users/<USER>/work space /secure-vault-cli/SecureVault/obj/Debug/net8.0/SecureVault.pdb
/Users/<USER>/work space /secure-vault-cli/SecureVault/obj/Debug/net8.0/SecureVault.genruntimeconfig.cache
/Users/<USER>/work space /secure-vault-cli/SecureVault/obj/Debug/net8.0/ref/SecureVault.dll
