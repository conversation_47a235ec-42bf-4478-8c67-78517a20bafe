{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"SecureVault/1.0.0": {"dependencies": {"VaultSharp": "1.17.5.1"}, "runtime": {"SecureVault.dll": {}}}, "VaultSharp/1.17.5.1": {"runtime": {"lib/net8.0/VaultSharp.dll": {"assemblyVersion": "1.17.5.1", "fileVersion": "1.17.5.1"}}}}}, "libraries": {"SecureVault/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "VaultSharp/1.17.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-1O/F+AQCkyK4K709pBDYEbDDfJ12OlaE5lnOs9dffq+KxqrnPxh8FIdQtEst9yBJmC7I0BptftzTJyRSwhZR/A==", "path": "vaultsharp/1.17.5.1", "hashPath": "vaultsharp.1.17.5.1.nupkg.sha512"}}}