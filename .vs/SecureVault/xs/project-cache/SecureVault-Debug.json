{"Format": 1, "ProjectReferences": [], "MetadataReferences": [{"FilePath": "/Users/<USER>/.nuget/packages/vaultsharp/1.17.5.1/lib/net8.0/VaultSharp.dll", "Aliases": [], "Framework": null}], "Files": ["/Users/<USER>/work space /secure-vault-cli/SecureVault/Program.cs", "/Users/<USER>/work space /secure-vault-cli/SecureVault/VaultExtensions.cs", "/Users/<USER>/work space /secure-vault-cli/SecureVault/obj/Debug/net8.0/SecureVault.GlobalUsings.g.cs", "/Users/<USER>/work space /secure-vault-cli/SecureVault/obj/Debug/net8.0/.NETCoreApp,Version=v8.0.AssemblyAttributes.cs", "/Users/<USER>/work space /secure-vault-cli/SecureVault/obj/Debug/net8.0/SecureVault.AssemblyInfo.cs", "/Users/<USER>/work space /secure-vault-cli/SecureVault/obj/Debug/net8.0/SecureVault.AssemblyInfo.cs", "/Users/<USER>/work space /secure-vault-cli/SecureVault/obj/Debug/net8.0/SecureVault.AssemblyInfo.cs", "/Users/<USER>/work space /secure-vault-cli/SecureVault/obj/Debug/net8.0/SecureVault.AssemblyInfo.cs"], "BuildActions": ["Compile", "Compile", "Compile", "Compile", "Compile", "Compile", "Compile", "Compile"], "Analyzers": ["/usr/local/share/dotnet/sdk/7.0.407/Sdks/Microsoft.NET.Sdk/analyzers/Microsoft.CodeAnalysis.CSharp.NetAnalyzers.dll", "/usr/local/share/dotnet/sdk/7.0.407/Sdks/Microsoft.NET.Sdk/analyzers/Microsoft.CodeAnalysis.NetAnalyzers.dll"], "AdditionalFiles": [], "EditorConfigFiles": ["/Users/<USER>/work space /secure-vault-cli/SecureVault/obj/Debug/net8.0/SecureVault.GeneratedMSBuildEditorConfig.editorconfig"], "DefineConstants": ["TRACE", "DEBUG", "NET", "NET8_0", "NETCOREAPP", "NET5_0_OR_GREATER", "NET6_0_OR_GREATER", "NET7_0_OR_GREATER", "NETCOREAPP1_0_OR_GREATER", "NETCOREAPP1_1_OR_GREATER", "NETCOREAPP2_0_OR_GREATER", "NETCOREAPP2_1_OR_GREATER", "NETCOREAPP2_2_OR_GREATER", "NETCOREAPP3_0_OR_GREATER", "NETCOREAPP3_1_OR_GREATER"], "IntermediateAssembly": "/Users/<USER>/work space /secure-vault-cli/SecureVault/obj/Debug/net8.0/SecureVault.dll"}